<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: index.php');
    exit();
}

// Récupérer et nettoyer les données
$nom = sanitizeInput($_POST['nom']);
$prenom = sanitizeInput($_POST['prenom']);
$email = sanitizeInput($_POST['email']);
$date_naissance = sanitizeInput($_POST['date_naissance']);
$genre = sanitizeInput($_POST['genre']);

// Sauvegarder les données pour les réafficher en cas d'erreur
$_SESSION['form_data'] = [
    'nom' => $nom,
    'prenom' => $prenom,
    'email' => $email,
    'date_naissance' => $date_naissance,
    'genre' => $genre
];

// Validation des données
$errors = [];

if (empty($nom) || strlen($nom) < 2) {
    $errors[] = "Le nom doit contenir au moins 2 caractères.";
}

if (empty($prenom) || strlen($prenom) < 2) {
    $errors[] = "Le prénom doit contenir au moins 2 caractères.";
}

if (empty($email) || !validateEmail($email)) {
    $errors[] = "Veuillez entrer une adresse email valide.";
}

if (empty($date_naissance) || !validateDate($date_naissance)) {
    $errors[] = "Veuillez entrer une date de naissance valide.";
} else {
    // Vérifier l'âge minimum (13 ans)
    $today = new DateTime();
    $birthDate = new DateTime($date_naissance);
    $age = $today->diff($birthDate)->y;
    
    if ($age < 13) {
        $errors[] = "Vous devez avoir au moins 13 ans pour créer un compte.";
    }
    
    if ($birthDate > $today) {
        $errors[] = "La date de naissance ne peut pas être dans le futur.";
    }
}

if (empty($genre) || !in_array($genre, ['masculin', 'feminin', 'autre'])) {
    $errors[] = "Veuillez sélectionner un genre valide.";
}

// Vérifier si l'email existe déjà
try {
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$email]);
    if ($stmt->fetch()) {
        $errors[] = "Cette adresse email est déjà utilisée.";
    }
} catch (PDOException $e) {
    $errors[] = "Erreur lors de la vérification de l'email.";
}

// S'il y a des erreurs, rediriger vers le formulaire
if (!empty($errors)) {
    redirectWithMessage('index.php', implode(' ', $errors), 'error');
}

// Traitement de la photo de profil
$photoFilename = null;
if (isset($_FILES['photo_profil']) && $_FILES['photo_profil']['error'] === UPLOAD_ERR_OK) {
    // On va d'abord créer l'utilisateur pour avoir son ID
    $tempUserId = 'temp_' . time();
} else if (isset($_FILES['photo_profil']) && $_FILES['photo_profil']['error'] !== UPLOAD_ERR_NO_FILE) {
    redirectWithMessage('index.php', 'Erreur lors du téléchargement de la photo.', 'error');
}

// Insérer l'utilisateur dans la base de données
try {
    $stmt = $pdo->prepare("INSERT INTO users (nom, prenom, email, date_naissance, genre) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute([$nom, $prenom, $email, $date_naissance, $genre]);
    
    $userId = $pdo->lastInsertId();
    
    // Traiter la photo de profil si elle existe
    if (isset($_FILES['photo_profil']) && $_FILES['photo_profil']['error'] === UPLOAD_ERR_OK) {
        $uploadResult = uploadProfilePhoto($_FILES['photo_profil'], $userId);
        
        if ($uploadResult['success']) {
            // Mettre à jour l'utilisateur avec le nom de fichier de la photo
            $stmt = $pdo->prepare("UPDATE users SET photo_profil = ? WHERE id = ?");
            $stmt->execute([$uploadResult['filename'], $userId]);
        } else {
            // La photo n'a pas pu être téléchargée, mais on continue quand même
            $_SESSION['photo_error'] = $uploadResult['message'];
        }
    }
    
    // Nettoyer les données de session
    unset($_SESSION['form_data']);
    
    // Rediriger vers la page de profil
    $_SESSION['user_id'] = $userId;
    redirectWithMessage('profile.php', 'Votre compte a été créé avec succès !', 'success');
    
} catch (PDOException $e) {
    redirectWithMessage('index.php', 'Erreur lors de la création du compte. Veuillez réessayer.', 'error');
}
?>
