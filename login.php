<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Si l'utilisateur est déjà connecté, rediriger vers le profil
if (isset($_SESSION['user_id'])) {
    header('Location: profile.php');
    exit();
}

// Traitement du formulaire de connexion
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitizeInput($_POST['email'] ?? '');
    
    if (empty($email) || !validateEmail($email)) {
        $error = "Veuillez entrer une adresse email valide.";
    } else {
        try {
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                $_SESSION['user_id'] = $user['id'];
                redirectWithMessage('profile.php', 'Connexion réussie !', 'success');
            } else {
                $error = "Aucun compte trouvé avec cette adresse email.";
            }
        } catch (PDOException $e) {
            $error = "Erreur lors de la connexion.";
        }
    }
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - Profil Utilisateur</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Se connecter</h1>
            <p>Entrez votre email pour accéder à votre profil</p>
        </div>
        
        <div class="form-container">
            <?php if (isset($error)): ?>
                <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" required 
                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                </div>
                
                <button type="submit" class="btn">Se connecter</button>
            </form>
            
            <div style="text-align: center; margin-top: 20px;">
                <p style="color: #6c757d;">Pas encore de compte ? <a href="index.php" style="color: #667eea; text-decoration: none;">Créer un compte</a></p>
            </div>
        </div>
    </div>
</body>
</html>
