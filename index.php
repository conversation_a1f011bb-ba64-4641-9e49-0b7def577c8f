<?php
session_start();
require_once 'includes/functions.php';
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inscription - Profil Utilisateur</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✨ Créer votre compte</h1>
            <p>Rejoignez notre communauté et créez votre profil professionnel</p>
        </div>

        <div class="form-container">
            <?php displayMessage(); ?>

            <form action="process_registration.php" method="POST" enctype="multipart/form-data">
                <div class="form-row">
                    <div class="form-group">
                        <label for="nom">Nom *</label>
                        <input type="text" id="nom" name="nom" required placeholder="Votre nom de famille"
                               value="<?php echo isset($_SESSION['form_data']['nom']) ? htmlspecialchars($_SESSION['form_data']['nom']) : ''; ?>">
                    </div>
                    <div class="form-group">
                        <label for="prenom">Prénom *</label>
                        <input type="text" id="prenom" name="prenom" required placeholder="Votre prénom"
                               value="<?php echo isset($_SESSION['form_data']['prenom']) ? htmlspecialchars($_SESSION['form_data']['prenom']) : ''; ?>">
                    </div>
                </div>

                <div class="form-group">
                    <label for="email">Email *</label>
                    <input type="email" id="email" name="email" required placeholder="<EMAIL>"
                           value="<?php echo isset($_SESSION['form_data']['email']) ? htmlspecialchars($_SESSION['form_data']['email']) : ''; ?>">
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="date_naissance">Date de naissance *</label>
                        <input type="date" id="date_naissance" name="date_naissance" required
                               value="<?php echo isset($_SESSION['form_data']['date_naissance']) ? htmlspecialchars($_SESSION['form_data']['date_naissance']) : ''; ?>">
                    </div>
                    <div class="form-group">
                        <label for="genre">Genre *</label>
                        <select id="genre" name="genre" required>
                            <option value="">Sélectionnez votre genre</option>
                            <option value="masculin" <?php echo (isset($_SESSION['form_data']['genre']) && $_SESSION['form_data']['genre'] === 'masculin') ? 'selected' : ''; ?>>Masculin</option>
                            <option value="feminin" <?php echo (isset($_SESSION['form_data']['genre']) && $_SESSION['form_data']['genre'] === 'feminin') ? 'selected' : ''; ?>>Féminin</option>
                            <option value="autre" <?php echo (isset($_SESSION['form_data']['genre']) && $_SESSION['form_data']['genre'] === 'autre') ? 'selected' : ''; ?>>Autre</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="photo_profil">Photo de profil (optionnel)</label>
                    <input type="file" id="photo_profil" name="photo_profil" accept="image/*">
                    <small style="color: #6c757d; font-size: 14px;">Formats acceptés: JPG, PNG, GIF. Taille maximum: 5MB</small>
                </div>

                <button type="submit" class="btn">Créer mon compte</button>
            </form>

            <div style="text-align: center; margin-top: 20px;">
                <p style="color: #6c757d;">Déjà un compte ? <a href="login.php" style="color: #667eea; text-decoration: none;">Se connecter</a></p>
            </div>
        </div>
    </div>

    <script>
        // Validation côté client
        document.querySelector('form').addEventListener('submit', function(e) {
            const nom = document.getElementById('nom').value.trim();
            const prenom = document.getElementById('prenom').value.trim();
            const email = document.getElementById('email').value.trim();
            const dateNaissance = document.getElementById('date_naissance').value;
            const genre = document.getElementById('genre').value;

            if (!nom || !prenom || !email || !dateNaissance || !genre) {
                e.preventDefault();
                alert('Veuillez remplir tous les champs obligatoires.');
                return;
            }

            // Vérifier que l'utilisateur a au moins 13 ans
            const today = new Date();
            const birthDate = new Date(dateNaissance);
            const age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();

            if (age < 13 || (age === 13 && monthDiff < 0) || (age === 13 && monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                e.preventDefault();
                alert('Vous devez avoir au moins 13 ans pour créer un compte.');
                return;
            }
        });
    </script>
</body>
</html>
<?php
// Nettoyer les données de session après affichage
if (isset($_SESSION['form_data'])) {
    unset($_SESSION['form_data']);
}
?>
