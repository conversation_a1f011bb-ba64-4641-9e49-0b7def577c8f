# Projet PHP - Profil Utilisateur

## Description
Application PHP permettant aux utilisateurs de créer un compte avec leurs informations personnelles et de gérer leur profil avec photo et compétences (similaire à LinkedIn).

## Fonctionnalités

### Inscription
- Formulaire avec nom, prénom, email, date de naissance, genre
- Upload de photo de profil (optionnel)
- Validation des données côté client et serveur
- Vérification de l'âge minimum (13 ans)

### Profil
- Affichage des informations personnelles
- Photo de profil avec initiales par défaut
- Gestion des compétences et liens (LinkedIn, GitHub, etc.)
- Mise à jour de la photo de profil
- Ajout/suppression de compétences

## Structure du projet

```
PHP/
├── config/
│   └── database.php          # Configuration base de données
├── includes/
│   └── functions.php         # Fonctions utilitaires
├── css/
│   └── style.css            # Styles CSS
├── uploads/                 # Dossier pour les photos (créé automatiquement)
├── index.php               # Page d'inscription
├── profile.php             # Page de profil
├── process_registration.php # Traitement inscription
├── update_profile.php      # Mise à jour profil
├── login.php               # Page de connexion
├── logout.php              # Déconnexion
├── database_setup.sql      # Script de création BDD
└── README.md              # Ce fichier
```

## Installation

### 1. Prérequis
- Serveur web (Apache/Nginx)
- PHP 7.4 ou supérieur
- MySQL 5.7 ou supérieur
- Extension PHP PDO activée

### 2. Configuration de la base de données

1. Créer la base de données :
```sql
mysql -u root -p < database_setup.sql
```

2. Modifier la configuration dans `config/database.php` :
```php
$host = 'localhost';        // Votre serveur MySQL
$dbname = 'user_profile_db'; // Nom de la base
$username = 'root';         // Votre utilisateur MySQL
$password = '';             // Votre mot de passe MySQL
```

### 3. Permissions
Assurez-vous que le dossier `uploads/` est accessible en écriture :
```bash
chmod 755 uploads/
```

## Utilisation

1. **Inscription** : Accédez à `index.php` pour créer un nouveau compte
2. **Connexion** : Utilisez `login.php` pour vous connecter avec votre email
3. **Profil** : Gérez votre profil sur `profile.php`

## Sécurité

- Validation et nettoyage de toutes les données utilisateur
- Protection contre les injections SQL avec PDO
- Validation des types de fichiers pour les uploads
- Limitation de la taille des fichiers (5MB)
- Sessions sécurisées

## Technologies utilisées

- **Backend** : PHP 7.4+
- **Base de données** : MySQL
- **Frontend** : HTML5, CSS3, JavaScript
- **Sécurité** : PDO, Sessions PHP

## Améliorations possibles

- Système de mot de passe
- Authentification à deux facteurs
- Redimensionnement automatique des images
- API REST
- Interface d'administration
- Système de notifications
