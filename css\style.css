/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Styles généraux */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
    position: relative;
}

/* Animated background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundMove 20s ease-in-out infinite;
}

@keyframes backgroundMove {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(1deg); }
}

.container {
    max-width: 500px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px 30px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: headerGlow 8s ease-in-out infinite;
}

@keyframes headerGlow {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(180deg); }
}

.header h1 {
    font-size: 2.2em;
    font-weight: 700;
    margin-bottom: 8px;
    position: relative;
    z-index: 1;
    letter-spacing: -0.02em;
}

.header p {
    font-size: 1em;
    opacity: 0.9;
    position: relative;
    z-index: 1;
    font-weight: 400;
}

.form-container, .profile-container {
    padding: 40px;
}

.form-group {
    margin-bottom: 28px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
    font-size: 14px;
    letter-spacing: 0.025em;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 16px 20px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 16px;
    font-family: inherit;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    background: rgba(255, 255, 255, 0.95);
    box-shadow:
        0 0 0 4px rgba(102, 126, 234, 0.1),
        0 4px 12px rgba(102, 126, 234, 0.15);
    transform: translateY(-1px);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

.form-row {
    display: flex;
    gap: 20px;
}

.form-row .form-group {
    flex: 1;
}

.btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 32px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    width: 100%;
    position: relative;
    overflow: hidden;
    font-family: inherit;
    letter-spacing: 0.025em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow:
        0 8px 25px rgba(102, 126, 234, 0.4),
        0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn:active {
    transform: translateY(0px);
}

.btn-secondary {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    margin-top: 12px;
}

.btn-secondary:hover {
    box-shadow:
        0 8px 25px rgba(107, 114, 128, 0.4),
        0 4px 12px rgba(107, 114, 128, 0.3);
}

/* Styles pour le profil */
.profile-container {
    max-width: 900px;
    margin: 0 auto;
}

.profile-header {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
}

.profile-photo {
    width: 160px;
    height: 160px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(255, 255, 255, 0.8);
    margin-bottom: 20px;
    box-shadow:
        0 8px 32px rgba(102, 126, 234, 0.3),
        0 0 0 4px rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.profile-photo:hover {
    transform: scale(1.05);
    box-shadow:
        0 12px 40px rgba(102, 126, 234, 0.4),
        0 0 0 6px rgba(102, 126, 234, 0.15);
}

.profile-info {
    background: rgba(248, 250, 252, 0.8);
    backdrop-filter: blur(10px);
    padding: 32px;
    border-radius: 16px;
    margin-bottom: 32px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.profile-info h3 {
    color: #374151;
    margin-bottom: 20px;
    font-size: 1.25em;
    font-weight: 600;
    position: relative;
    padding-bottom: 12px;
}

.profile-info h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #495057;
}

.info-value {
    color: #6c757d;
}

.skills-section {
    margin-top: 30px;
}

.skill-item {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.skill-info {
    flex: 1;
}

.skill-type {
    background: #667eea;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.skill-name {
    font-weight: 600;
    margin: 8px 0 4px 0;
}

.skill-value {
    color: #6c757d;
    word-break: break-all;
}

.add-skill-form {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px;
}

.alert {
    padding: 16px 20px;
    border-radius: 12px;
    margin-bottom: 24px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.alert::before {
    content: '';
    width: 20px;
    height: 20px;
    border-radius: 50%;
    flex-shrink: 0;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: #065f46;
    border-color: rgba(16, 185, 129, 0.2);
}

.alert-success::before {
    background: #10b981;
    content: '✓';
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.alert-error {
    background: rgba(239, 68, 68, 0.1);
    color: #991b1b;
    border-color: rgba(239, 68, 68, 0.2);
}

.alert-error::before {
    background: #ef4444;
    content: '!';
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    font-weight: bold;
}

/* Animations d'entrée */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.container {
    animation: fadeInUp 0.6s ease-out;
}

.form-group {
    animation: slideInRight 0.6s ease-out;
    animation-fill-mode: both;
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.2s; }
.form-group:nth-child(3) { animation-delay: 0.3s; }
.form-group:nth-child(4) { animation-delay: 0.4s; }
.form-group:nth-child(5) { animation-delay: 0.5s; }

/* Styles pour les liens */
a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -2px;
    left: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
}

a:hover::after {
    width: 100%;
}

a:hover {
    color: #764ba2;
}

/* Responsive */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .container {
        margin: 0;
        border-radius: 20px;
        max-width: 100%;
    }

    .header {
        padding: 30px 20px;
    }

    .header h1 {
        font-size: 1.8em;
    }

    .form-container, .profile-container {
        padding: 30px 20px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 14px 16px;
        font-size: 16px; /* Évite le zoom sur iOS */
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.6em;
    }

    .form-container, .profile-container {
        padding: 20px 16px;
    }
}
