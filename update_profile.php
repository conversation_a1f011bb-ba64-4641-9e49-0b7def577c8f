<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Vérifier si l'utilisateur est connecté
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: profile.php');
    exit();
}

$userId = $_SESSION['user_id'];
$action = $_POST['action'] ?? '';

switch ($action) {
    case 'update_photo':
        handlePhotoUpdate($pdo, $userId);
        break;
        
    case 'add_skill':
        handleAddSkill($pdo, $userId);
        break;
        
    case 'delete_skill':
        handleDeleteSkill($pdo, $userId);
        break;
        
    default:
        redirectWithMessage('profile.php', 'Action non reconnue.', 'error');
}

function handlePhotoUpdate($pdo, $userId) {
    if (!isset($_FILES['new_photo']) || $_FILES['new_photo']['error'] !== UPLOAD_ERR_OK) {
        redirectWithMessage('profile.php', 'Veuillez sélectionner une photo valide.', 'error');
    }
    
    $uploadResult = uploadProfilePhoto($_FILES['new_photo'], $userId);
    
    if ($uploadResult['success']) {
        try {
            // Récupérer l'ancienne photo pour la supprimer
            $user = getUserById($pdo, $userId);
            $oldPhoto = $user['photo_profil'];
            
            // Mettre à jour la base de données
            $stmt = $pdo->prepare("UPDATE users SET photo_profil = ? WHERE id = ?");
            $stmt->execute([$uploadResult['filename'], $userId]);
            
            // Supprimer l'ancienne photo si elle existe
            if ($oldPhoto && file_exists('uploads/' . $oldPhoto)) {
                unlink('uploads/' . $oldPhoto);
            }
            
            redirectWithMessage('profile.php', 'Photo de profil mise à jour avec succès !', 'success');
        } catch (PDOException $e) {
            redirectWithMessage('profile.php', 'Erreur lors de la mise à jour de la photo.', 'error');
        }
    } else {
        redirectWithMessage('profile.php', $uploadResult['message'], 'error');
    }
}

function handleAddSkill($pdo, $userId) {
    $skillType = sanitizeInput($_POST['skill_type'] ?? '');
    $skillName = sanitizeInput($_POST['skill_name'] ?? '');
    $skillValue = sanitizeInput($_POST['skill_value'] ?? '');
    
    // Validation
    $errors = [];
    
    if (empty($skillType) || !in_array($skillType, ['competence', 'linkedin', 'github', 'website', 'autre'])) {
        $errors[] = 'Veuillez sélectionner un type valide.';
    }
    
    if (empty($skillName) || strlen($skillName) < 2) {
        $errors[] = 'Le nom doit contenir au moins 2 caractères.';
    }
    
    if (empty($skillValue) || strlen($skillValue) < 2) {
        $errors[] = 'La valeur doit contenir au moins 2 caractères.';
    }
    
    // Validation spéciale pour les URLs
    if (in_array($skillType, ['linkedin', 'github', 'website'])) {
        if (!filter_var($skillValue, FILTER_VALIDATE_URL)) {
            $errors[] = 'Veuillez entrer une URL valide pour ce type de lien.';
        }
    }
    
    if (!empty($errors)) {
        redirectWithMessage('profile.php', implode(' ', $errors), 'error');
    }
    
    // Ajouter la compétence
    try {
        if (addUserSkill($pdo, $userId, $skillType, $skillName, $skillValue)) {
            redirectWithMessage('profile.php', 'Compétence ajoutée avec succès !', 'success');
        } else {
            redirectWithMessage('profile.php', 'Erreur lors de l\'ajout de la compétence.', 'error');
        }
    } catch (PDOException $e) {
        redirectWithMessage('profile.php', 'Erreur lors de l\'ajout de la compétence.', 'error');
    }
}

function handleDeleteSkill($pdo, $userId) {
    $skillId = intval($_POST['skill_id'] ?? 0);
    
    if ($skillId <= 0) {
        redirectWithMessage('profile.php', 'ID de compétence invalide.', 'error');
    }
    
    try {
        if (deleteUserSkill($pdo, $skillId, $userId)) {
            redirectWithMessage('profile.php', 'Compétence supprimée avec succès !', 'success');
        } else {
            redirectWithMessage('profile.php', 'Erreur lors de la suppression de la compétence.', 'error');
        }
    } catch (PDOException $e) {
        redirectWithMessage('profile.php', 'Erreur lors de la suppression de la compétence.', 'error');
    }
}
?>
