<?php
// Fonctions utilitaires

function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function validateDate($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

function uploadProfilePhoto($file, $userId) {
    $uploadDir = 'uploads/';
    
    // Créer le dossier s'il n'existe pas
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }
    
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    $maxSize = 5 * 1024 * 1024; // 5MB
    
    if (!in_array($file['type'], $allowedTypes)) {
        return ['success' => false, 'message' => 'Type de fichier non autorisé. Utilisez JPG, PNG ou GIF.'];
    }
    
    if ($file['size'] > $maxSize) {
        return ['success' => false, 'message' => 'Le fichier est trop volumineux. Maximum 5MB.'];
    }
    
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'profile_' . $userId . '_' . time() . '.' . $extension;
    $filepath = $uploadDir . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => true, 'filename' => $filename];
    } else {
        return ['success' => false, 'message' => 'Erreur lors du téléchargement du fichier.'];
    }
}

function getUserById($pdo, $userId) {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function getUserSkills($pdo, $userId) {
    $stmt = $pdo->prepare("SELECT * FROM user_skills WHERE user_id = ? ORDER BY date_creation DESC");
    $stmt->execute([$userId]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function addUserSkill($pdo, $userId, $type, $name, $value) {
    $stmt = $pdo->prepare("INSERT INTO user_skills (user_id, type_skill, nom_skill, valeur_skill) VALUES (?, ?, ?, ?)");
    return $stmt->execute([$userId, $type, $name, $value]);
}

function deleteUserSkill($pdo, $skillId, $userId) {
    $stmt = $pdo->prepare("DELETE FROM user_skills WHERE id = ? AND user_id = ?");
    return $stmt->execute([$skillId, $userId]);
}

function formatDate($date) {
    $dateObj = new DateTime($date);
    return $dateObj->format('d/m/Y');
}

function calculateAge($birthDate) {
    $today = new DateTime();
    $birth = new DateTime($birthDate);
    $age = $today->diff($birth);
    return $age->y;
}

function getSkillTypeLabel($type) {
    $labels = [
        'competence' => 'Compétence',
        'linkedin' => 'LinkedIn',
        'github' => 'GitHub',
        'website' => 'Site Web',
        'autre' => 'Autre'
    ];
    return $labels[$type] ?? 'Inconnu';
}

function redirectWithMessage($url, $message, $type = 'success') {
    session_start();
    $_SESSION['message'] = $message;
    $_SESSION['message_type'] = $type;
    header("Location: $url");
    exit();
}

function displayMessage() {
    if (isset($_SESSION['message'])) {
        $type = $_SESSION['message_type'] ?? 'success';
        $class = $type === 'success' ? 'alert-success' : 'alert-error';
        echo "<div class='alert $class'>" . htmlspecialchars($_SESSION['message']) . "</div>";
        unset($_SESSION['message']);
        unset($_SESSION['message_type']);
    }
}
?>
