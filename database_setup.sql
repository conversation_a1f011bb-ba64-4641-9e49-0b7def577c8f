-- Script de création de la base de données
CREATE DATABASE IF NOT EXISTS user_profile_db CHARACTER SET utf8 COLLATE utf8_general_ci;

USE user_profile_db;

-- Table des utilisateurs
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    date_naissance DATE NOT NULL,
    genre ENUM('masculin', 'feminin', 'autre') NOT NULL,
    photo_profil VARCHAR(255) DEFAULT NULL,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des compétences/liens
CREATE TABLE IF NOT EXISTS user_skills (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type_skill ENUM('competence', 'linkedin', 'github', 'website', 'autre') NOT NULL,
    nom_skill VARCHAR(100) NOT NULL,
    valeur_skill TEXT NOT NULL,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Index pour optimiser les requêtes
CREATE INDEX idx_user_email ON users(email);
CREATE INDEX idx_user_skills_user_id ON user_skills(user_id);
