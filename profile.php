<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Vérifier si l'utilisateur est connecté
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit();
}

$userId = $_SESSION['user_id'];

// Récupérer les informations de l'utilisateur
$user = getUserById($pdo, $userId);
if (!$user) {
    session_destroy();
    header('Location: index.php');
    exit();
}

// Récupérer les compétences de l'utilisateur
$skills = getUserSkills($pdo, $userId);
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profil - <?php echo htmlspecialchars($user['prenom'] . ' ' . $user['nom']); ?></title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Mon Profil</h1>
            <p>Bienvenue, <?php echo htmlspecialchars($user['prenom']); ?> !</p>
        </div>
        
        <div class="profile-container">
            <?php displayMessage(); ?>
            
            <?php if (isset($_SESSION['photo_error'])): ?>
                <div class="alert alert-error">
                    Photo de profil : <?php echo htmlspecialchars($_SESSION['photo_error']); ?>
                </div>
                <?php unset($_SESSION['photo_error']); ?>
            <?php endif; ?>
            
            <div class="profile-header">
                <?php if ($user['photo_profil']): ?>
                    <img src="uploads/<?php echo htmlspecialchars($user['photo_profil']); ?>" 
                         alt="Photo de profil" class="profile-photo">
                <?php else: ?>
                    <div class="profile-photo" style="background: #667eea; display: flex; align-items: center; justify-content: center; color: white; font-size: 48px; font-weight: bold;">
                        <?php echo strtoupper(substr($user['prenom'], 0, 1) . substr($user['nom'], 0, 1)); ?>
                    </div>
                <?php endif; ?>
                
                <h2><?php echo htmlspecialchars($user['prenom'] . ' ' . $user['nom']); ?></h2>
                <p style="color: #6c757d; font-size: 18px;"><?php echo calculateAge($user['date_naissance']); ?> ans</p>
            </div>
            
            <div class="profile-info">
                <h3>Informations personnelles</h3>
                <div class="info-item">
                    <span class="info-label">Email :</span>
                    <span class="info-value"><?php echo htmlspecialchars($user['email']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Date de naissance :</span>
                    <span class="info-value"><?php echo formatDate($user['date_naissance']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Genre :</span>
                    <span class="info-value"><?php echo ucfirst(htmlspecialchars($user['genre'])); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Membre depuis :</span>
                    <span class="info-value"><?php echo formatDate($user['date_creation']); ?></span>
                </div>
            </div>
            
            <!-- Section pour mettre à jour la photo de profil -->
            <div class="add-skill-form">
                <h3>Mettre à jour la photo de profil</h3>
                <form action="update_profile.php" method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="update_photo">
                    <div class="form-group">
                        <label for="new_photo">Nouvelle photo de profil</label>
                        <input type="file" id="new_photo" name="new_photo" accept="image/*" required>
                        <small style="color: #6c757d; font-size: 14px;">Formats acceptés: JPG, PNG, GIF. Taille maximum: 5MB</small>
                    </div>
                    <button type="submit" class="btn">Mettre à jour la photo</button>
                </form>
            </div>
            
            <!-- Section des compétences -->
            <div class="skills-section">
                <h3>Compétences et liens</h3>
                
                <?php if (empty($skills)): ?>
                    <p style="color: #6c757d; text-align: center; padding: 20px;">
                        Aucune compétence ou lien ajouté pour le moment.
                    </p>
                <?php else: ?>
                    <?php foreach ($skills as $skill): ?>
                        <div class="skill-item">
                            <div class="skill-info">
                                <span class="skill-type"><?php echo getSkillTypeLabel($skill['type_skill']); ?></span>
                                <div class="skill-name"><?php echo htmlspecialchars($skill['nom_skill']); ?></div>
                                <div class="skill-value">
                                    <?php if (in_array($skill['type_skill'], ['linkedin', 'github', 'website'])): ?>
                                        <a href="<?php echo htmlspecialchars($skill['valeur_skill']); ?>" target="_blank" style="color: #667eea;">
                                            <?php echo htmlspecialchars($skill['valeur_skill']); ?>
                                        </a>
                                    <?php else: ?>
                                        <?php echo htmlspecialchars($skill['valeur_skill']); ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <form action="update_profile.php" method="POST" style="margin-left: 15px;">
                                <input type="hidden" name="action" value="delete_skill">
                                <input type="hidden" name="skill_id" value="<?php echo $skill['id']; ?>">
                                <button type="submit" class="btn btn-secondary" style="padding: 8px 15px; font-size: 14px;" 
                                        onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette compétence ?')">
                                    Supprimer
                                </button>
                            </form>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
                
                <!-- Formulaire pour ajouter une compétence -->
                <div class="add-skill-form">
                    <h4>Ajouter une compétence ou un lien</h4>
                    <form action="update_profile.php" method="POST">
                        <input type="hidden" name="action" value="add_skill">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="skill_type">Type</label>
                                <select id="skill_type" name="skill_type" required>
                                    <option value="">Sélectionnez un type</option>
                                    <option value="competence">Compétence</option>
                                    <option value="linkedin">LinkedIn</option>
                                    <option value="github">GitHub</option>
                                    <option value="website">Site Web</option>
                                    <option value="autre">Autre</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="skill_name">Nom</label>
                                <input type="text" id="skill_name" name="skill_name" required 
                                       placeholder="Ex: PHP, Mon LinkedIn, etc.">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="skill_value">Valeur/Lien</label>
                            <textarea id="skill_value" name="skill_value" rows="3" required 
                                      placeholder="Ex: Niveau avancé, https://linkedin.com/in/monprofil, etc."></textarea>
                        </div>
                        <button type="submit" class="btn">Ajouter</button>
                    </form>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="logout.php" class="btn btn-secondary" style="display: inline-block; text-decoration: none;">
                    Se déconnecter
                </a>
            </div>
        </div>
    </div>
</body>
</html>
